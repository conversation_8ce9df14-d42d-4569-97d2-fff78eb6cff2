{% extends "base.html" %}

{% block title %}Sales Forecast - Make Prediction{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h2 class="mb-0">Make Prediction</h2>
            </div>
            <div class="card-body">
                {% if model_name %}
                <div class="alert alert-info mb-4">
                    <h5>Current Model: {{ model_name }}</h5>
                    {% if feature_columns and feature_columns|length > 0 %}
                    <p>Enter values for the following features to get a sales prediction.</p>
                    {% else %}
                    <p><strong>Time-series model detected!</strong> Only a date is required for prediction (no additional features needed).</p>
                    {% endif %}
                </div>

                <form method="POST" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="ds" class="form-label">Date</label>
                        <input type="date" class="form-control" id="ds" name="ds" required>
                        <div class="invalid-feedback">Please select a date.</div>
                    </div>

                    {% if feature_columns and feature_columns|length > 0 %}
                    <h5 class="mt-4 mb-3">Feature Values</h5>
                    <div class="row">
                        {% for feature in feature_columns %}
                        <div class="col-md-6">
                            <div class="mb-3 feature-input">
                                <label for="{{ feature }}" class="form-label">{{ feature }}</label>
                                <input type="number" step="any" class="form-control" id="{{ feature }}" name="{{ feature }}" required>
                                <div class="invalid-feedback">Please enter a valid number.</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-success mt-3">
                        <i class="bi bi-check-circle"></i> <strong>Ready to predict!</strong> This model uses time-series analysis and only needs a date.
                    </div>
                    {% endif %}

                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-graph-up"></i> Generate Prediction
                    </button>
                </form>
                {% else %}
                <div class="alert alert-warning">
                    <h5>No Model Available</h5>
                    <p>Please upload a dataset and train a model first.</p>
                    <a href="{{ url_for('upload_dataset') }}" class="btn btn-primary">Upload Dataset</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
